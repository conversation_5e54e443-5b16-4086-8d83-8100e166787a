#!/usr/bin/env node
/*
  Provisions local DynamoDB tables and GSIs for development.
  Requires DynamoDB Local running at DYNAMODB_ENDPOINT (default http://localhost:8000).
*/

const { DynamoDBClient, CreateTableCommand, UpdateTableCommand, ListTablesCommand } = require("@aws-sdk/client-dynamodb");

const endpoint = process.env.DYNAMODB_ENDPOINT || "http://localhost:8000";
const region = process.env.AWS_REGION || "us-east-1";

const client = new DynamoDBClient({
  region,
  endpoint,
  credentials: { accessKeyId: "local", secretAccessKey: "local" },
});

async function ensureTable(params, gsiUpdates = []) {
  const tables = await client.send(new ListTablesCommand({}));
  if (!tables.TableNames.includes(params.TableName)) {
    console.log(`Creating table ${params.TableName}...`);
    await client.send(new CreateTableCommand(params));
  } else {
    console.log(`Table ${params.TableName} exists`);
  }
  for (const gsi of gsiUpdates) {
    try {
      console.log(`Updating ${params.TableName} to add/update GSI ${gsi.IndexName}...`);
      await client.send(new UpdateTableCommand({
        TableName: params.TableName,
        AttributeDefinitions: gsi.AttributeDefinitions,
        GlobalSecondaryIndexUpdates: [
          { Create: gsi }
        ],
      }));
    } catch (e) {
      console.log(`Skipping GSI ${gsi.IndexName}: ${e.message}`);
    }
  }
}

(async () => {
  // Jobs
  const JOBS_TABLE = process.env.JOBS_TABLE || "jobs-dev";
  await ensureTable({
    TableName: JOBS_TABLE,
    AttributeDefinitions: [
      { AttributeName: "job_id", AttributeType: "S" },
      { AttributeName: "customer_id", AttributeType: "S" },
      { AttributeName: "created_at", AttributeType: "S" },
    ],
    KeySchema: [
      { AttributeName: "job_id", KeyType: "HASH" },
    ],
    BillingMode: "PAY_PER_REQUEST",
  }, [
    {
      IndexName: "gsi_customer_created",
      KeySchema: [
        { AttributeName: "customer_id", KeyType: "HASH" },
        { AttributeName: "created_at", KeyType: "RANGE" },
      ],
      Projection: { ProjectionType: "ALL" },
      ProvisionedThroughput: { ReadCapacityUnits: 1, WriteCapacityUnits: 1 },
      AttributeDefinitions: [
        { AttributeName: "customer_id", AttributeType: "S" },
        { AttributeName: "created_at", AttributeType: "S" },
      ],
    },
  ]);

  // Users
  const USERS_TABLE = process.env.USERS_TABLE || "youtube-chapter-infra-dev-users";
  await ensureTable({
    TableName: USERS_TABLE,
    AttributeDefinitions: [
      { AttributeName: "id", AttributeType: "S" },
      { AttributeName: "email", AttributeType: "S" },
    ],
    KeySchema: [
      { AttributeName: "id", KeyType: "HASH" },
    ],
    BillingMode: "PAY_PER_REQUEST",
  }, [
    {
      IndexName: "gsi_email",
      KeySchema: [
        { AttributeName: "email", KeyType: "HASH" },
      ],
      Projection: { ProjectionType: "ALL" },
      ProvisionedThroughput: { ReadCapacityUnits: 1, WriteCapacityUnits: 1 },
      AttributeDefinitions: [
        { AttributeName: "email", AttributeType: "S" },
      ],
    },
  ]);

  // Transcripts
  const TRANSCRIPTS_TABLE = process.env.TRANSCRIPTS_TABLE || "transcripts-dev";
  await ensureTable({
    TableName: TRANSCRIPTS_TABLE,
    AttributeDefinitions: [
      { AttributeName: "video_id", AttributeType: "S" },
    ],
    KeySchema: [
      { AttributeName: "video_id", KeyType: "HASH" },
    ],
    BillingMode: "PAY_PER_REQUEST",
  });

  // Chapters
  const CHAPTERS_TABLE = process.env.CHAPTERS_TABLE || "chapters-dev";
  await ensureTable({
    TableName: CHAPTERS_TABLE,
    AttributeDefinitions: [
      { AttributeName: "video_id", AttributeType: "S" },
      { AttributeName: "c", AttributeType: "S" },
    ],
    KeySchema: [
      { AttributeName: "video_id", KeyType: "HASH" },
      { AttributeName: "c", KeyType: "RANGE" },
    ],
    BillingMode: "PAY_PER_REQUEST",
  });

  // Videos
  const VIDEOS_TABLE = process.env.VIDEOS_TABLE || "videos-dev";
  await ensureTable({
    TableName: VIDEOS_TABLE,
    AttributeDefinitions: [
      { AttributeName: "video_id", AttributeType: "S" },
      { AttributeName: "customer_id", AttributeType: "S" },
      { AttributeName: "created_at", AttributeType: "S" },
    ],
    KeySchema: [
      { AttributeName: "video_id", KeyType: "HASH" },
    ],
    BillingMode: "PAY_PER_REQUEST",
  }, [
    {
      IndexName: "gsi_customer_created",
      KeySchema: [
        { AttributeName: "customer_id", KeyType: "HASH" },
        { AttributeName: "created_at", KeyType: "RANGE" },
      ],
      Projection: { ProjectionType: "ALL" },
      ProvisionedThroughput: { ReadCapacityUnits: 1, WriteCapacityUnits: 1 },
      AttributeDefinitions: [
        { AttributeName: "customer_id", AttributeType: "S" },
        { AttributeName: "created_at", AttributeType: "S" },
      ],
    },
  ]);

  console.log("Local DynamoDB tables ensured.");
})().catch((e) => {
  console.error(e);
  process.exit(1);
});


