{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:ddb": "cross-env NEXT_PUBLIC_STORAGE=dynamodb DYNAMODB_ENDPOINT=http://localhost:8000 AWS_REGION=us-east-1 next dev --turbopack", "ddb:provision": "node scripts/provision-ddb.js | cat", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.637.0", "@aws-sdk/lib-dynamodb": "^3.637.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@uploadthing/react": "^7.3.2", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "groq-sdk": "^0.30.0", "jose": "^6.0.12", "lucide-react": "^0.539.0", "next": "15.4.6", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "uploadthing": "^7.7.3", "ytdl-core": "^4.11.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}