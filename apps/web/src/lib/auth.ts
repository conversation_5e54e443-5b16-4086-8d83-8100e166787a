import { SignJWT, jwtVerify } from 'jose';
import bcrypt from 'bcryptjs';
import { getUserByEmail, getUserById, createUser, updateUserLastLogin } from '@/lib/users';
import { grantSignupBonus } from '@/lib/billing';

export type AuthContext = {
  user_id: string;
  customer_id: string;
  email: string;
  plan: "starter" | "creator" | "pro";
  is_authenticated: boolean;
};

export type User = {
  id: string;
  email: string;
  password_hash: string;
  customer_id: string;
  plan: "starter" | "creator" | "pro";
  created_at: string;
  last_login?: string;
  email_verified: boolean;
  stripe_customer_id?: string;
};

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-change-in-production'
);

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export async function signJWT(payload: { user_id: string; email: string }): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(JWT_SECRET);
}

export async function verifyJWT(token: string): Promise<{ user_id: string; email: string; expired?: boolean } | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as { user_id: string; email: string };
  } catch (error: any) {
    // Check if the error is due to token expiration
    if (error?.code === 'ERR_JWT_EXPIRED' || error?.message?.includes('expired')) {
      console.log('JWT token expired');
      return { user_id: '', email: '', expired: true };
    }
    console.log('JWT verification failed:', error?.message);
    return null;
  }
}

export async function parseAuth(headers: Headers): Promise<AuthContext> {
  // 1) Prefer httpOnly cookie `auth_token`
  const cookieHeader = headers.get("cookie") || headers.get("Cookie") || "";
  const cookieMap = new Map<string, string>();
  for (const part of cookieHeader.split(";")) {
    const [k, ...v] = part.trim().split("=");
    if (!k) continue;
    cookieMap.set(k, decodeURIComponent(v.join("=")));
  }
  const cookieToken = cookieMap.get("auth_token");
  if (cookieToken) {
    const jwtPayload = await verifyJWT(cookieToken);
    if (jwtPayload && !jwtPayload.expired) {
      const user = await getUserById(jwtPayload.user_id);
      if (user) {
        await updateUserLastLogin(user.id);
        return {
          user_id: user.id,
          customer_id: user.customer_id,
          email: user.email,
          plan: user.plan,
          is_authenticated: true,
        };
      }
    } else if (jwtPayload?.expired) {
      // Token is expired, log this for debugging
      console.log('Cookie token expired, user needs to re-authenticate');
    }
  }

  // 2) Fallback: Authorization: Bearer <token>
  const auth = headers.get("authorization") || headers.get("Authorization");

  if (auth && auth.startsWith("Bearer ")) {
    const token = auth.slice("Bearer ".length).trim();

    if (token) {
      // Try JWT token first
      const jwtPayload = await verifyJWT(token);
      if (jwtPayload && !jwtPayload.expired) {
        const user = await getUserById(jwtPayload.user_id);
        if (user) {
          // Update last login
          await updateUserLastLogin(user.id);

          return {
            user_id: user.id,
            customer_id: user.customer_id,
            email: user.email,
            plan: user.plan,
            is_authenticated: true,
          };
        }
      } else if (jwtPayload?.expired) {
        console.log('Bearer token expired, user needs to re-authenticate');
      }

      // Fallback to API key for backwards compatibility
      const { lookupApiKey } = await import("@/lib/apikeys");
      const apiKeyRec = lookupApiKey(token);
      if (apiKeyRec) {
        return {
          user_id: apiKeyRec.customer_id, // Use customer_id as user_id for API keys
          customer_id: apiKeyRec.customer_id,
          email: `api-key@${apiKeyRec.customer_id}`,
          plan: apiKeyRec.plan,
          is_authenticated: true,
        };
      }
    }
  }

  // Return unauthenticated context
  return {
    user_id: "",
    customer_id: "",
    email: "",
    plan: "starter",
    is_authenticated: false,
  };
}

export async function signUp(email: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> {
  try {
    // Validate input
    if (!email || !password) {
      return { success: false, error: "Email and password are required" };
    }

    if (password.length < 8) {
      return { success: false, error: "Password must be at least 8 characters" };
    }

    if (!isValidEmail(email)) {
      return { success: false, error: "Invalid email format" };
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return { success: false, error: "User already exists with this email" };
    }

    // Create user
    const passwordHash = await hashPassword(password);
    const user = await createUser({
      email,
      password_hash: passwordHash,
      plan: "starter",
    });
    // Grant signup bonus of 30 minutes (one-time)
    try { grantSignupBonus(user.customer_id, 30); } catch {}

    return { success: true, user };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, error: "Failed to create account" };
  }
}

export async function signIn(email: string, password: string): Promise<{ success: boolean; token?: string; user?: User; error?: string }> {
  try {
    // Validate input
    if (!email || !password) {
      return { success: false, error: "Email and password are required" };
    }

    // Get user
    const user = await getUserByEmail(email);
    if (!user) {
      return { success: false, error: "Invalid email or password" };
    }

    // Verify password
    const isValid = await verifyPassword(password, user.password_hash);
    if (!isValid) {
      return { success: false, error: "Invalid email or password" };
    }

    // Generate JWT
    const token = await signJWT({ user_id: user.id, email: user.email });

    // Update last login
    await updateUserLastLogin(user.id);

    return { success: true, token, user };
  } catch (error) {
    console.error('Signin error:', error);
    return { success: false, error: "Failed to sign in" };
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}


