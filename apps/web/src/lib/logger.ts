type LogLevel = "debug" | "info" | "warn" | "error";

function log(level: LogLevel, message: string, context?: Record<string, unknown>) {
  const payload = context ? { message, ...context } : { message };
  const line = `${new Date().toISOString()} ${level.toUpperCase()} ${payload.message}`;
  // Use console under the hood; could be swapped for a real logger later
  switch (level) {
    case "debug":
      if (process.env.NODE_ENV !== "production") console.debug(line, context || {});
      break;
    case "info":
      console.info(line, context || {});
      break;
    case "warn":
      console.warn(line, context || {});
      break;
    case "error":
      console.error(line, context || {});
      break;
  }
}

export const logger = {
  debug: (message: string, context?: Record<string, unknown>) => log("debug", message, context),
  info: (message: string, context?: Record<string, unknown>) => log("info", message, context),
  warn: (message: string, context?: Record<string, unknown>) => log("warn", message, context),
  error: (message: string, context?: Record<string, unknown>) => log("error", message, context),
};


