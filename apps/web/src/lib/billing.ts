type Plan = "starter" | "creator" | "pro";

const PLAN_ALLOWANCE_MIN = {
  starter: 120,
  creator: 400,
  pro: 1600,
} as const satisfies Record<Plan, number>;

const wallet = new Map<string, number>(); // customer_id -> minutes_used (this period)
const bonusWallet = new Map<string, number>(); // customer_id -> remaining bonus minutes (e.g., signup bonus)

export function getPlanAllowance(plan: Plan): number {
  return PLAN_ALLOWANCE_MIN[plan];
}

export function getUsageMinutes(customerId: string): number {
  return wallet.get(customerId) ?? 0;
}

export function canConsume(customerId: string, plan: Plan, minutes: number): boolean {
  const used = getUsageMinutes(customerId);
  const bonus = getBonusRemaining(customerId);
  return used + minutes <= getPlanAllowance(plan) + bonus;
}

export function recordUsage(customerId: string, minutes: number): void {
  // Consume available bonus minutes first
  const currentBonus = getBonusRemaining(customerId);
  if (currentBonus > 0) {
    const bonusConsumed = Math.min(currentBonus, minutes);
    bonusWallet.set(customerId, currentBonus - bonusConsumed);
    const remainingToCharge = minutes - bonusConsumed;
    if (remainingToCharge <= 0) return;
    const used = getUsageMinutes(customerId);
    wallet.set(customerId, used + remainingToCharge);
    return;
  }
  const used = getUsageMinutes(customerId);
  wallet.set(customerId, used + minutes);
}

export function getRemainingMinutes(customerId: string, plan: Plan): number {
  return Math.max(0, getPlanAllowance(plan) + getBonusRemaining(customerId) - getUsageMinutes(customerId));
}

export type UsageSummary = {
  customer_id: string;
  plan: Plan;
  used: number;
  allowance: number;
  remaining: number;
};

export function getUsageSummary(customerId: string, plan: Plan): UsageSummary {
  const used = getUsageMinutes(customerId);
  const allowance = getPlanAllowance(plan);
  return {
    customer_id: customerId,
    plan,
    used,
    allowance,
    remaining: Math.max(0, allowance + getBonusRemaining(customerId) - used),
  };
}

// Bonus management
export function grantSignupBonus(customerId: string, minutes: number): void {
  const current = bonusWallet.get(customerId) ?? 0;
  bonusWallet.set(customerId, current + Math.max(0, Math.floor(minutes)));
}

export function getBonusRemaining(customerId: string): number {
  return bonusWallet.get(customerId) ?? 0;
}


