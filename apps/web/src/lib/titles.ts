import type { Chapter } from "./types";

function toOrdinal(n: number): string {
  const s = ["th", "st", "nd", "rd"], v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
}

export type TitleStyle = "concise" | "descriptive";

// Common video content patterns for better title generation
const CONTENT_PATTERNS = {
  intro: ["introduction", "intro", "welcome", "getting started", "overview", "what is", "about"],
  setup: ["setup", "installation", "configure", "install", "preparation", "requirements"],
  tutorial: ["how to", "tutorial", "step by step", "guide", "walkthrough", "demo"],
  explanation: ["explanation", "theory", "concept", "understanding", "why", "what"],
  example: ["example", "demo", "demonstration", "practice", "hands-on", "exercise"],
  conclusion: ["conclusion", "summary", "wrap up", "final", "ending", "recap"],
  tips: ["tips", "tricks", "best practices", "advice", "recommendations", "pro tips"],
  troubleshooting: ["troubleshooting", "debugging", "fixing", "problems", "issues", "errors"]
};

function generateMeaningfulTitle(index: number, totalChapters: number, style: TitleStyle): string {
  const position = index / (totalChapters - 1); // 0 to 1

  if (style === "descriptive") {
    if (position < 0.1) return "Introduction and Overview";
    if (position < 0.3) return `Getting Started - Part ${index}`;
    if (position < 0.7) return `Main Content - Section ${index}`;
    if (position < 0.9) return `Advanced Topics - Part ${index - Math.floor(totalChapters * 0.7) + 1}`;
    return "Summary and Conclusion";
  } else {
    // Concise style
    if (position < 0.1) return "Introduction";
    if (position < 0.3) return `Setup & Basics`;
    if (position < 0.7) return `Core Content ${index - 1}`;
    if (position < 0.9) return `Advanced Topics`;
    return "Conclusion";
  }
}

function enhanceTitleWithContext(title: string, index: number, totalChapters: number): string {
  const lowerTitle = title.toLowerCase();

  // Check for content patterns and enhance accordingly
  for (const [category, keywords] of Object.entries(CONTENT_PATTERNS)) {
    if (keywords.some(keyword => lowerTitle.includes(keyword))) {
      switch (category) {
        case "intro":
          return index === 0 ? "Introduction & Overview" : `Getting Started - ${title}`;
        case "setup":
          return `Setup: ${title.replace(/setup|installation|install/gi, '').trim() || 'Configuration'}`;
        case "tutorial":
          return `Tutorial: ${title.replace(/how to|tutorial|step by step/gi, '').trim() || 'Step-by-Step Guide'}`;
        case "conclusion":
          return index === totalChapters - 1 ? "Summary & Next Steps" : `Wrap-up: ${title}`;
        default:
          return title;
      }
    }
  }

  // If no pattern matches, create a contextual title
  const position = index / Math.max(1, totalChapters - 1);
  if (position < 0.2) return `Getting Started: ${title}`;
  if (position > 0.8) return `Advanced: ${title}`;
  return title;
}

export function refineChapterTitles(
  chapters: Chapter[],
  style: TitleStyle = "concise"
): Chapter[] {
  return chapters.map((c, i) => {
    const idx = i + 1;

    // If title is generic (Section X), generate a meaningful one
    if (!c.title || c.title.startsWith("Section ") || c.title.startsWith("Chapter ")) {
      const meaningfulTitle = generateMeaningfulTitle(i, chapters.length, style);
      return { ...c, title: meaningfulTitle };
    }

    // If title exists but could be enhanced, improve it
    const enhancedTitle = enhanceTitleWithContext(c.title, i, chapters.length);
    return { ...c, title: enhancedTitle };
  });
}

// Generate SEO-friendly titles for YouTube
export function generateSEOTitle(chapters: Chapter[], videoTitle?: string): string {
  if (!chapters.length) return "Video Chapters";

  const topics = chapters
    .map(c => c.title)
    .filter(title => !title.startsWith("Section") && !title.startsWith("Chapter"))
    .slice(0, 3) // Take first 3 meaningful topics
    .join(" | ");

  if (topics && videoTitle) {
    return `${videoTitle} - ${topics}`;
  } else if (topics) {
    return `Complete Guide: ${topics}`;
  } else if (videoTitle) {
    return `${videoTitle} - Chapter Breakdown`;
  }

  return "Video Tutorial with Timestamps";
}


