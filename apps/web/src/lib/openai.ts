type ChatMessage = { role: 'system' | 'user' | 'assistant'; content: string };

function makeBaseUrl(): { baseUrl: string; key: string | null; provider: 'openrouter' | 'openai' | null } {
  const orKey = process.env.OPENROUTER_API_KEY;
  if (orKey) {
    return { baseUrl: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1', key: or<PERSON><PERSON>, provider: 'openrouter' };
  }
  const oaKey = process.env.OPENAI_API_KEY;
  if (oaKey) {
    return { baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1', key: oaKey, provider: 'openai' };
  }
  return { baseUrl: '', key: null, provider: null };
}

export async function chatJSON(
  messages: ChatMessage[],
  options?: { model?: string; maxTokens?: number }
): Promise<any | null> {
  const { baseUrl, key, provider } = makeBaseUrl();
  if (!key || !baseUrl || !provider) return null;

  let model = options?.model || process.env.OPENAI_MODEL || 'google/gemini-2.5-flash';
  if (provider === 'openrouter') {
    // Normalize model names when using OpenRouter
    const override = process.env.OPENROUTER_MODEL;
    if (override) model = override;
    if (!model.includes('/')) model = `openai/${model}`; // e.g., openai/gpt-4o-mini
  }

  try {
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${key}`,
      'Content-Type': 'application/json',
    };
    if (provider === 'openrouter') {
      const site = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL || '';
      if (site) headers['HTTP-Referer'] = /^https?:\/\//.test(site) ? site : `https://${site}`;
      headers['X-Title'] = process.env.OPENROUTER_TITLE || 'YouTube Chapter Generator';
    }

    const res = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model,
        messages,
        response_format: { type: 'json_object' },
        max_tokens: options?.maxTokens ?? 2000,
        temperature: 0.2,
      }),
      next: { revalidate: 0 },
    });
    if (!res.ok) return null;
    const json = await res.json();
    const content: string | undefined = json?.choices?.[0]?.message?.content;
    if (!content) return null;
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  } catch {
    return null;
  }
}


