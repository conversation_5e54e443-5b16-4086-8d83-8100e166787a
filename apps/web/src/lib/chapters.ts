import type { Chapter } from "./types";
import { secondsToHms } from "./time";
import type { TranscriptSegment } from "./transcribe";
import { chatJSON } from "./openai";

export type ChapterDensity = "auto" | "sparse" | "dense";

function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value));
}

export function computeChapterCount(durationMinutes: number, density: ChapterDensity): number {
  const base = Math.ceil(durationMinutes / 6);
  const densityMultiplier = density === "sparse" ? 0.75 : density === "dense" ? 1.25 : 1.0;
  const n = Math.round(base * densityMultiplier);
  return clamp(n, 6, 25);
}

export function segmentByLength(
  durationSeconds: number,
  density: ChapterDensity = "auto"
): Chapter[] {
  const durationMinutes = Math.ceil(durationSeconds / 60);
  const numChapters = computeChapterCount(durationMinutes, density);
  if (numChapters <= 0) return [];

  // Create more natural segment lengths with some variation
  const chapters: Chapter[] = [];
  const baseSegmentLength = durationSeconds / numChapters;

  for (let i = 0; i < numChapters; i += 1) {
    // Add some variation to segment lengths to make them feel more natural
    const variation = (Math.random() - 0.5) * 0.3; // ±15% variation
    const adjustedLength = baseSegmentLength * (1 + variation);

    const startSec = i === 0 ? 0 : Math.floor(chapters[i - 1].duration_sec + (chapters[i - 1].start === "0:00:00" ? 0 : parseTimeToSeconds(chapters[i - 1].start)));
    const isLast = i === numChapters - 1;
    const endSec = isLast ? durationSeconds : Math.min(durationSeconds, startSec + Math.floor(adjustedLength));

    // Ensure minimum chapter length of 30 seconds
    const actualEndSec = Math.max(startSec + 30, endSec);

    const chapter: Chapter = {
      start: secondsToHms(startSec),
      end: secondsToHms(actualEndSec),
      duration_sec: Math.max(30, actualEndSec - startSec),
      title: generateContextualTitle(i, numChapters, durationMinutes),
    };
    chapters.push(chapter);
  }

  // Adjust the last chapter to end exactly at video duration
  if (chapters.length > 0) {
    const lastChapter = chapters[chapters.length - 1];
    lastChapter.end = secondsToHms(durationSeconds);
    lastChapter.duration_sec = durationSeconds - parseTimeToSeconds(lastChapter.start);
  }

  return chapters;
}

function parseTimeToSeconds(timeStr: string): number {
  const parts = timeStr.split(':').map(Number);
  if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  } else if (parts.length === 2) {
    return parts[0] * 60 + parts[1];
  }
  return parts[0] || 0;
}

function generateContextualTitle(index: number, totalChapters: number, durationMinutes: number): string {
  const position = index / Math.max(1, totalChapters - 1);

  // For very short videos (< 5 min), use simple numbering
  if (durationMinutes < 5) {
    return `Part ${index + 1}`;
  }

  // For longer videos, use contextual titles based on position
  if (index === 0) {
    return "Introduction & Overview";
  } else if (index === totalChapters - 1) {
    return "Summary & Conclusion";
  } else if (position < 0.3) {
    return `Getting Started - Part ${index}`;
  } else if (position < 0.7) {
    return `Main Content - Section ${index}`;
  } else {
    return `Advanced Topics - Part ${index}`;
  }
}

export function segmentByTranscript(
  segments: TranscriptSegment[],
  targetCount: number
): Chapter[] {
  if (!segments.length) return [];

  const totalSeconds = Math.max(1, Math.floor(segments[segments.length - 1].end - segments[0].start));
  const buckets: { start: number; end: number; segments: TranscriptSegment[] }[] = [];
  const quota = totalSeconds / Math.max(1, targetCount);

  let bucketStart = segments[0].start;
  let acc = 0;
  let lastEnd = bucketStart;
  let currentBucketSegments: TranscriptSegment[] = [];

  for (const s of segments) {
    const dur = Math.max(0, s.end - (lastEnd || s.start));
    acc += dur;
    lastEnd = s.end;
    currentBucketSegments.push(s);

    // Look for natural break points in the transcript
    const isNaturalBreak = detectNaturalBreak(s.text);
    const hasMinimumLength = acc >= quota * 0.7; // At least 70% of target length
    const shouldBreak = (acc >= quota || isNaturalBreak) && hasMinimumLength && buckets.length < targetCount - 1;

    if (shouldBreak) {
      buckets.push({
        start: bucketStart,
        end: s.end,
        segments: [...currentBucketSegments]
      });
      bucketStart = s.end;
      acc = 0;
      currentBucketSegments = [];
    }
  }

  // Add the final bucket
  if (currentBucketSegments.length > 0) {
    buckets.push({
      start: bucketStart,
      end: lastEnd,
      segments: currentBucketSegments
    });
  }

  return buckets.map((b, i) => ({
    start: secondsToHms(Math.max(0, Math.floor(b.start))),
    end: secondsToHms(Math.max(0, Math.floor(b.end))),
    duration_sec: Math.max(0, Math.floor(b.end - b.start)),
    title: generateTitleFromTranscript(b.segments, i, buckets.length),
  }));
}

export async function generateChaptersWithOpenAI(
  segments: TranscriptSegment[],
  options?: { titleStyle?: "concise" | "descriptive"; targetCount?: number }
): Promise<Chapter[] | null> {
  console.log('🤖 generateChaptersWithOpenAI called with:', {
    segmentCount: segments?.length || 0,
    titleStyle: options?.titleStyle,
    targetCount: options?.targetCount
  });

  if (!segments?.length) {
    console.log('❌ No segments provided to generateChaptersWithOpenAI');
    return null;
  }
  const maxPreview = Math.min(100, segments.length);
  const preview = segments.slice(0, maxPreview).map(s => ({ start: s.start, end: s.end, text: s.text }));
  const system: { role: 'system'; content: string } = {
    role: 'system',
    content: 'You are a precise chaptering assistant. Given a transcript with timestamps, return a JSON object with a `chapters` array where each item has: start (HH:MM:SS), end (HH:MM:SS), title (string under 60 chars). Titles must be clear and non-redundant.',
  };
  const user: { role: 'user'; content: string } = {
    role: 'user',
    content: [
      'Create high-quality chapters from this transcript. Follow these rules:',
      '- Evenly cover the full duration without overlaps, keep chronological order',
      `- Prefer ${options?.titleStyle || 'concise'} titles; avoid punctuation at the end`,
      options?.targetCount ? `- Aim for about ${options.targetCount} chapters` : '- Use a reasonable number of chapters',
      '',
      'Transcript preview (first ~100 segments):',
      JSON.stringify(preview),
    ].join('\n'),
  };
  console.log('🤖 Calling chatJSON for OpenAI chapter generation...');
  const resp = await chatJSON([system, user]);
  console.log('🤖 OpenAI response:', resp);

  const out = resp?.chapters;
  if (!Array.isArray(out)) {
    console.log('❌ OpenAI did not return valid chapters array:', out);
    return null;
  }

  console.log('✅ OpenAI returned', out.length, 'chapters');
  const toSeconds = (t: string) => {
    const p = String(t || '').split(':').map(n => parseInt(n, 10));
    if (p.length === 3) return p[0] * 3600 + p[1] * 60 + p[2];
    if (p.length === 2) return p[0] * 60 + p[1];
    return p[0] || 0;
  };
  return out
    .map((c: any) => ({
      start: secondsToHms(toSeconds(c.start)),
      end: secondsToHms(toSeconds(c.end)),
      duration_sec: Math.max(0, toSeconds(c.end) - toSeconds(c.start)),
      title: String(c.title || '').slice(0, 80) || 'Section',
    }))
    .filter((c: Chapter) => c.duration_sec > 0);
}

function detectNaturalBreak(text: string): boolean {
  const lowerText = text.toLowerCase().trim();

  // Common transition phrases that indicate chapter breaks
  const transitionPhrases = [
    "now let's", "next we", "moving on", "let's move", "now we're going to",
    "the next step", "next up", "now that we", "let's talk about",
    "another thing", "something else", "let's look at", "now let's discuss",
    "in this section", "for this part", "let's switch", "now i want to",
    "let's go ahead", "so now", "alright so", "okay so", "now what we",
    "let's see how", "let's explore", "time to", "now it's time"
  ];

  // Conclusion phrases
  const conclusionPhrases = [
    "in conclusion", "to summarize", "to wrap up", "in summary",
    "that's it for", "that concludes", "to recap", "so in summary"
  ];

  // Question phrases that might start new sections
  const questionPhrases = [
    "what is", "how do", "why do", "when should", "where can",
    "what about", "how about", "what if", "but what"
  ];

  return transitionPhrases.some(phrase => lowerText.includes(phrase)) ||
         conclusionPhrases.some(phrase => lowerText.includes(phrase)) ||
         questionPhrases.some(phrase => lowerText.startsWith(phrase));
}

function generateTitleFromTranscript(segments: TranscriptSegment[], index: number, totalChapters: number): string {
  if (!segments.length) {
    return `Section ${index + 1}`;
  }

  // Combine all text from segments
  const fullText = segments.map(s => s.text).join(' ').toLowerCase();

  // Extract key topics and concepts
  const title = extractKeyTopics(fullText, index, totalChapters);

  return title || generateContextualTitle(index, totalChapters, Math.ceil(segments[segments.length - 1].end / 60));
}

function extractKeyTopics(text: string, index: number, totalChapters: number): string {
  // Remove filler words and clean text
  const cleanText = text
    .replace(/\b(um|uh|like|you know|so|well|actually|basically|literally)\b/g, '')
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Common technical and educational keywords
  const keywords = {
    intro: ['introduction', 'intro', 'welcome', 'overview', 'getting started', 'what is'],
    setup: ['setup', 'install', 'configuration', 'requirements', 'preparation'],
    tutorial: ['how to', 'step by step', 'tutorial', 'guide', 'walkthrough'],
    code: ['code', 'coding', 'programming', 'function', 'variable', 'class'],
    design: ['design', 'layout', 'styling', 'css', 'html', 'interface'],
    data: ['data', 'database', 'api', 'json', 'response', 'request'],
    testing: ['test', 'testing', 'debug', 'error', 'fix', 'troubleshoot'],
    deployment: ['deploy', 'deployment', 'production', 'server', 'hosting'],
    conclusion: ['conclusion', 'summary', 'recap', 'final', 'wrap up', 'next steps']
  };

  // Find the most relevant category
  let bestCategory = '';
  let bestScore = 0;

  for (const [category, words] of Object.entries(keywords)) {
    const score = words.reduce((acc, word) => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      const matches = (cleanText.match(regex) || []).length;
      return acc + matches;
    }, 0);

    if (score > bestScore) {
      bestScore = score;
      bestCategory = category;
    }
  }

  // Generate title based on category and position
  const position = index / Math.max(1, totalChapters - 1);

  switch (bestCategory) {
    case 'intro':
      return index === 0 ? 'Introduction & Overview' : 'Getting Started';
    case 'setup':
      return 'Setup & Configuration';
    case 'tutorial':
      return `Tutorial: ${extractMainTopic(cleanText)}`;
    case 'code':
      return `Coding: ${extractMainTopic(cleanText)}`;
    case 'design':
      return `Design & Styling`;
    case 'data':
      return `Working with Data`;
    case 'testing':
      return `Testing & Debugging`;
    case 'deployment':
      return `Deployment & Production`;
    case 'conclusion':
      return 'Summary & Next Steps';
    default:
      // Fallback: extract the most mentioned meaningful words
      return extractMainTopic(cleanText) || generateContextualTitle(index, totalChapters, 10);
  }
}

function extractMainTopic(text: string): string {
  // Split into words and count frequency
  const words = text.split(/\s+/)
    .filter(word => word.length > 3) // Filter out short words
    .filter(word => !['this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other'].includes(word));

  const wordCount = new Map<string, number>();
  words.forEach(word => {
    wordCount.set(word, (wordCount.get(word) || 0) + 1);
  });

  // Get the most frequent meaningful word
  const sortedWords = Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 2);

  if (sortedWords.length > 0) {
    const mainTopic = sortedWords[0][0];
    return mainTopic.charAt(0).toUpperCase() + mainTopic.slice(1);
  }

  return '';
}

/**
 * Enhance basic chapters with OpenAI-generated titles (without transcript)
 */
export async function enhanceChaptersWithOpenAI(
  basicChapters: Chapter[],
  options?: { titleStyle?: "concise" | "descriptive"; videoId?: string; duration?: number }
): Promise<Chapter[] | null> {
  console.log('🤖 enhanceChaptersWithOpenAI called with:', {
    chapterCount: basicChapters?.length || 0,
    titleStyle: options?.titleStyle,
    videoId: options?.videoId,
    duration: options?.duration
  });

  if (!basicChapters?.length) {
    console.log('❌ No basic chapters provided to enhanceChaptersWithOpenAI');
    return null;
  }

  try {
    const { chatJSON } = await import('./openai');

    const system = {
      role: 'system' as const,
      content: 'You are a precise chaptering assistant. Given basic chapter timestamps, return a JSON object with a `chapters` array where each item has: start (HH:MM:SS), end (HH:MM:SS), title (string under 60 chars). Create descriptive, engaging titles that would be appropriate for a video tutorial or educational content.'
    };

    const durationMin = Math.ceil((options?.duration || 600) / 60);
    const chapterInfo = basicChapters.map((c, i) =>
      `Chapter ${i + 1}: ${c.start} - ${c.end} (${Math.round(c.duration_sec)}s)`
    ).join('\n');

    const user = {
      role: 'user' as const,
      content: `Create high-quality chapter titles for this ${durationMin}-minute video. Replace generic titles with descriptive, engaging ones that would help viewers navigate the content.

Current chapters:
${chapterInfo}

Guidelines:
- Make titles descriptive and engaging (avoid "Part 1", "Section 2", etc.)
- Consider the video position (intro, main content, conclusion)
- Keep titles under 60 characters
- Make them SEO-friendly and searchable
- Use ${options?.titleStyle || 'concise'} style

Return JSON with the same timestamps but improved titles.`
    };

    console.log('🤖 Calling chatJSON for chapter enhancement...');
    const resp = await chatJSON([system, user]);
    console.log('🤖 OpenAI enhancement response:', resp);

    const out = resp?.chapters;
    if (!Array.isArray(out)) {
      console.log('❌ OpenAI did not return valid chapters array for enhancement:', out);
      return null;
    }

    console.log('✅ OpenAI enhanced', out.length, 'chapters');

    const toSeconds = (t: string) => {
      const p = String(t || '').split(':').map(n => parseInt(n, 10));
      if (p.length === 3) return p[0] * 3600 + p[1] * 60 + p[2];
      if (p.length === 2) return p[0] * 60 + p[1];
      return p[0] || 0;
    };

    return out
      .map((c: any) => ({
        start: secondsToHms(toSeconds(c.start)),
        end: secondsToHms(toSeconds(c.end)),
        duration_sec: Math.max(0, toSeconds(c.end) - toSeconds(c.start)),
        title: String(c.title || '').slice(0, 80) || 'Section',
      }))
      .filter((c: Chapter) => c.duration_sec > 0);

  } catch (error) {
    console.error('❌ enhanceChaptersWithOpenAI error:', error);
    return null;
  }
}


