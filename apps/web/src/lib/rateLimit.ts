// Simple in-memory rate limiter
// In production, use Redis or a proper rate limiting service

type RateLimitConfig = {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
};

type RateLimitEntry = {
  count: number;
  resetTime: number;
};

const rateLimitStore = new Map<string, RateLimitEntry>();
const useUpstash = Boolean(process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN);
const globalAny = globalThis as unknown as Record<string, any>;

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000);

export function rateLimit(
  identifier: string,
  config: RateLimitConfig
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = identifier;
  
  let entry = rateLimitStore.get(key);
  
  // If no entry or window has expired, create new entry
  if (!entry || now > entry.resetTime) {
    entry = {
      count: 1,
      resetTime: now + config.windowMs,
    };
    rateLimitStore.set(key, entry);
    
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: entry.resetTime,
    };
  }
  
  // Check if limit exceeded
  if (entry.count >= config.maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime,
    };
  }
  
  // Increment count
  entry.count++;
  rateLimitStore.set(key, entry);
  
  return {
    allowed: true,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime,
  };
}

// Optional Redis-backed limiter (Upstash). Falls back to in-memory if not configured.
export async function rateLimitCheck(
  identifier: string,
  config: RateLimitConfig
): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  if (!useUpstash) {
    return rateLimit(identifier, config);
  }
  // Lazily construct per-config limiter using late dynamic import to avoid build-time resolution
  const cacheKey = `__rl:${config.windowMs}:${config.maxRequests}`;
  try {
    if (!globalAny[cacheKey]) {
      const dynamicImport = new Function('s', 'return import(s)') as (s: string) => Promise<any>;
      const [ratelimitMod, redisMod] = await Promise.all([
        dynamicImport('@upstash/ratelimit'),
        dynamicImport('@upstash/redis'),
      ]);
      const Ratelimit = ratelimitMod.Ratelimit || ratelimitMod.default;
      const Redis = redisMod.Redis || redisMod.default;
      const redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL as string,
        token: process.env.UPSTASH_REDIS_REST_TOKEN as string,
      });
      const seconds = Math.max(1, Math.ceil(config.windowMs / 1000));
      const limiter = new Ratelimit({
        redis,
        limiter: Ratelimit.slidingWindow(config.maxRequests, `${seconds} s`),
        analytics: false,
        prefix: 'rl',
      });
      globalAny[cacheKey] = limiter;
    }
    const limiter = globalAny[cacheKey];
    const result = await limiter.limit(identifier);
    return {
      allowed: result.success,
      remaining: result.remaining,
      resetTime: result.reset,
    };
  } catch {
    // If optional deps are missing, fall back to in-memory limiter
    return rateLimit(identifier, config);
  }
}

export function getClientIP(headers: Headers): string {
  // Try various headers for client IP
  const forwarded = headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  const cfConnectingIP = headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // Fallback
  return 'unknown';
}

// Rate limit configurations
export const RATE_LIMITS = {
  // API endpoints
  GENERATE_API: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 requests per minute per IP
  },
  
  // Auth endpoints
  AUTH_API: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes per IP
  },
  
  // General API
  GENERAL_API: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute per IP
  },
  
  // Per-user limits (authenticated)
  USER_GENERATE: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 generations per minute per user
  },
} as const;

export function createRateLimitResponse(resetTime: number) {
  const resetTimeSeconds = Math.ceil(resetTime / 1000);
  
  return Response.json(
    { 
      error: "Rate limit exceeded",
      message: "Too many requests. Please try again later.",
      resetTime: resetTimeSeconds,
    },
    { 
      status: 429,
      headers: {
        'Retry-After': Math.ceil((resetTime - Date.now()) / 1000).toString(),
        'X-RateLimit-Reset': resetTimeSeconds.toString(),
      },
    }
  );
}
