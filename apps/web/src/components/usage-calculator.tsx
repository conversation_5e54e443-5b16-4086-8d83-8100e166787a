"use client";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Calculator, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  ArrowRight
} from "lucide-react";

type UsageCalculatorProps = {
  currentUsage: {
    used: number;
    allowance: number;
    remaining: number;
  };
  currentPlan: "starter" | "creator" | "pro";
  className?: string;
};

const PLAN_DETAILS = {
  starter: {
    name: "Starter",
    minutes: 120,
    price: 0,
    costPerMinute: 0
  },
  creator: {
    name: "Creator", 
    minutes: 400,
    price: 9,
    costPerMinute: 0.0225
  },
  pro: {
    name: "Pro",
    minutes: 1600,
    price: 29,
    costPerMinute: 0.018125
  }
};

export function UsageCalculator({ currentUsage, currentPlan, className }: UsageCalculatorProps) {
  const [estimatedMinutes, setEstimatedMinutes] = useState(60);
  
  const currentPlanDetails = PLAN_DETAILS[currentPlan];
  const usagePercentage = (currentUsage.used / currentUsage.allowance) * 100;
  
  const projectedUsage = currentUsage.used + estimatedMinutes;
  const willExceedLimit = projectedUsage > currentUsage.allowance;
  
  const getRecommendedPlan = () => {
    if (projectedUsage <= PLAN_DETAILS.starter.minutes) return "starter";
    if (projectedUsage <= PLAN_DETAILS.creator.minutes) return "creator";
    return "pro";
  };

  const recommendedPlan = getRecommendedPlan();
  const needsUpgrade = recommendedPlan !== currentPlan;

  const calculateCost = (minutes: number, plan: keyof typeof PLAN_DETAILS) => {
    const planDetails = PLAN_DETAILS[plan];
    if (plan === "starter") return 0;
    return planDetails.price;
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Calculator className="h-5 w-5 text-blue-600" />
          <h3 className="font-medium">Usage Calculator</h3>
        </div>

        {/* Current usage */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Current Usage</span>
            <span className="text-sm font-medium">
              {currentUsage.used}/{currentUsage.allowance} min
            </span>
          </div>
          <Progress value={usagePercentage} className="h-2" />
          <div className="flex justify-between text-xs text-gray-500">
            <span>{currentUsage.remaining} min remaining</span>
            <span>{Math.round(usagePercentage)}% used</span>
          </div>
        </div>

        {/* Usage estimator */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Estimate additional usage this month:</label>
          <div className="flex items-center gap-3">
            <input
              type="range"
              min="0"
              max="500"
              step="10"
              value={estimatedMinutes}
              onChange={(e) => setEstimatedMinutes(Number(e.target.value))}
              className="flex-1"
            />
            <div className="flex items-center gap-1 min-w-[80px]">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium">{estimatedMinutes} min</span>
            </div>
          </div>
        </div>

        {/* Projection */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
          <h4 className="font-medium text-sm">Projected Total Usage</h4>
          <div className="flex items-center justify-between">
            <span className="text-sm">Total minutes:</span>
            <span className="font-medium">{projectedUsage} min</span>
          </div>
          
          {willExceedLimit && (
            <div className="flex items-center gap-2 text-orange-600 text-sm">
              <AlertTriangle className="h-4 w-4" />
              <span>Will exceed {currentPlanDetails.name} plan limit</span>
            </div>
          )}
        </div>

        {/* Plan recommendations */}
        {needsUpgrade && (
          <div className="border border-blue-200 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-sm text-blue-900 dark:text-blue-100">
                Recommended Plan
              </h4>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">
                  {PLAN_DETAILS[recommendedPlan].name} Plan
                </span>
                <span className="font-medium">
                  ${PLAN_DETAILS[recommendedPlan].price}/month
                </span>
              </div>
              <div className="text-xs text-gray-600">
                {PLAN_DETAILS[recommendedPlan].minutes} minutes included
              </div>
            </div>

            <Button size="sm" className="w-full">
              <span>Upgrade to {PLAN_DETAILS[recommendedPlan].name}</span>
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        )}

        {!needsUpgrade && !willExceedLimit && (
          <div className="flex items-center gap-2 text-green-600 text-sm">
            <CheckCircle className="h-4 w-4" />
            <span>Your current plan covers projected usage</span>
          </div>
        )}

        {/* Plan comparison */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Plan Comparison</h4>
          <div className="grid grid-cols-3 gap-2 text-xs">
            {Object.entries(PLAN_DETAILS).map(([key, plan]) => (
              <div
                key={key}
                className={`p-2 rounded border text-center ${
                  key === currentPlan
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200"
                }`}
              >
                <div className="font-medium">{plan.name}</div>
                <div className="text-gray-600">{plan.minutes} min</div>
                <div className="font-medium">
                  {plan.price === 0 ? "Free" : `$${plan.price}/mo`}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p>• Only processed video minutes count toward usage</p>
          <p>• Failed jobs are not billed</p>
          <p>• Usage resets monthly on your billing date</p>
        </div>
      </div>
    </Card>
  );
}
