"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  ChevronDown, 
  ChevronUp, 
  Settings, 
  HelpCircle, 
  Bookmark,
  Sliders
} from "lucide-react";

type ProgressiveOptionsProps = {
  density: "auto" | "sparse" | "dense";
  titleStyle: "concise" | "descriptive";
  includeSummaries: boolean;
  onDensityChange: (value: "auto" | "sparse" | "dense") => void;
  onTitleStyleChange: (value: "concise" | "descriptive") => void;
  onIncludeSummariesChange: (value: boolean) => void;
  className?: string;
};

export function ProgressiveOptions({
  density,
  titleStyle,
  includeSummaries,
  onDensityChange,
  onTitleStyleChange,
  onIncludeSummariesChange,
  className
}: ProgressiveOptionsProps) {
  const [showBasicOptions, setShowBasicOptions] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [rememberChoices, setRememberChoices] = useState(false);

  // Load rememberChoices from localStorage on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setRememberChoices(localStorage.getItem("rememberChoices") === "true");
    }
  }, []);

  const handleRememberChoicesChange = (checked: boolean) => {
    setRememberChoices(checked);
    localStorage.setItem("rememberChoices", checked.toString());
    
    if (checked) {
      // Save current choices as defaults
      localStorage.setItem("defaultOptions", JSON.stringify({
        density,
        titleStyle,
        includeSummaries
      }));
    } else {
      localStorage.removeItem("defaultOptions");
    }
  };

  const densityOptions = [
    {
      value: "auto" as const,
      label: "Auto",
      description: "Smart detection based on content",
      recommended: true
    },
    {
      value: "sparse" as const,
      label: "Sparse",
      description: "Fewer, longer chapters (3-5 min each)"
    },
    {
      value: "dense" as const,
      label: "Dense", 
      description: "More, shorter chapters (1-2 min each)"
    }
  ];

  const titleStyleOptions = [
    {
      value: "concise" as const,
      label: "Concise",
      description: "Short, punchy titles",
      example: "Introduction"
    },
    {
      value: "descriptive" as const,
      label: "Descriptive",
      description: "Detailed explanations",
      example: "Introduction to the main topic"
    }
  ];

  return (
    <TooltipProvider>
      <div className={`space-y-3 ${className}`}>
        {/* Phase 1: Always visible - just the toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Options</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowBasicOptions(!showBasicOptions)}
            className="flex items-center gap-1"
          >
            <Settings className="h-4 w-4" />
            {showBasicOptions ? "Hide" : "Show"}
            {showBasicOptions ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>

        {/* Phase 2: Basic options */}
        {showBasicOptions && (
          <Card className="p-4 space-y-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <label className="text-sm font-medium">Chapter Density</label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-3 w-3 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Controls how many chapters are generated</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="grid grid-cols-3 gap-2">
                {densityOptions.map(({ value, label, description, recommended }) => (
                  <button
                    key={value}
                    onClick={() => onDensityChange(value)}
                    className={`p-3 rounded border text-center transition-colors relative ${
                      density === value
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    {recommended && (
                      <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
                    )}
                    <div className="font-medium text-sm">{label}</div>
                    <div className="text-xs text-gray-500 mt-1">{description}</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                className="flex items-center gap-1"
              >
                <Sliders className="h-4 w-4" />
                Advanced options
                {showAdvancedOptions ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </div>
          </Card>
        )}

        {/* Phase 3: Advanced options */}
        {showBasicOptions && showAdvancedOptions && (
          <Card className="p-4 space-y-4 border-dashed">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <label className="text-sm font-medium">Title Style</label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-3 w-3 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>How detailed should chapter titles be?</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {titleStyleOptions.map(({ value, label, description, example }) => (
                  <button
                    key={value}
                    onClick={() => onTitleStyleChange(value)}
                    className={`p-3 rounded border text-left transition-colors ${
                      titleStyle === value
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="font-medium text-sm">{label}</div>
                    <div className="text-xs text-gray-500 mt-1">{description}</div>
                    <div className="text-xs text-gray-400 mt-1 italic">e.g. "{example}"</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center gap-2 text-sm">
                <Checkbox 
                  checked={includeSummaries} 
                  onCheckedChange={onIncludeSummariesChange}
                />
                <span>Include chapter summaries</span>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-3 w-3 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add brief descriptions for each chapter</p>
                  </TooltipContent>
                </Tooltip>
              </label>

              <label className="flex items-center gap-2 text-sm">
                <Checkbox 
                  checked={rememberChoices} 
                  onCheckedChange={handleRememberChoicesChange}
                />
                <span className="flex items-center gap-1">
                  <Bookmark className="h-3 w-3" />
                  Remember my choices
                </span>
              </label>
            </div>
          </Card>
        )}
      </div>
    </TooltipProvider>
  );
}
