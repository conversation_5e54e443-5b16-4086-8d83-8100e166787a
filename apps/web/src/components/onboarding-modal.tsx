"use client";
import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>Header, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Play, 
  Settings, 
  Download, 
  ChevronRight, 
  ChevronLeft,
  CheckCircle 
} from "lucide-react";

type OnboardingStep = {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
};

type OnboardingModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
};

export function OnboardingModal({ isOpen, onClose, onComplete }: OnboardingModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState({
    chapterDensity: "auto" as "auto" | "sparse" | "dense",
    titleStyle: "concise" as "concise" | "descriptive",
    emailNotifications: false
  });

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: "Welcome to ChapterGen!",
      description: "Let's get you started with generating YouTube chapters in just a few steps.",
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Play className="h-8 w-8 text-blue-600" />
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              ChapterGen helps you create timestamped chapters for YouTube videos automatically. 
              Just paste a URL and we'll handle the rest!
            </p>
          </div>
          <div className="grid grid-cols-3 gap-4 text-center text-sm">
            <div>
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-green-600 font-semibold">1</span>
              </div>
              <p className="text-gray-600">Paste URL</p>
            </div>
            <div>
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-blue-600 font-semibold">2</span>
              </div>
              <p className="text-gray-600">Generate</p>
            </div>
            <div>
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-purple-600 font-semibold">3</span>
              </div>
              <p className="text-gray-600">Export</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "preferences",
      title: "Set Your Preferences",
      description: "Customize how chapters are generated to match your style.",
      content: (
        <div className="space-y-4">
          <Card className="p-4">
            <label className="block text-sm font-medium mb-2">Chapter Density</label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: "sparse", label: "Sparse", desc: "Fewer, longer chapters" },
                { value: "auto", label: "Auto", desc: "Smart detection" },
                { value: "dense", label: "Dense", desc: "More, shorter chapters" }
              ].map(({ value, label, desc }) => (
                <button
                  key={value}
                  onClick={() => setPreferences(prev => ({ ...prev, chapterDensity: value as any }))}
                  className={`p-3 rounded border text-center transition-colors ${
                    preferences.chapterDensity === value
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="font-medium text-sm">{label}</div>
                  <div className="text-xs text-gray-500">{desc}</div>
                </button>
              ))}
            </div>
          </Card>

          <Card className="p-4">
            <label className="block text-sm font-medium mb-2">Title Style</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { value: "concise", label: "Concise", desc: "Short, punchy titles" },
                { value: "descriptive", label: "Descriptive", desc: "Detailed explanations" }
              ].map(({ value, label, desc }) => (
                <button
                  key={value}
                  onClick={() => setPreferences(prev => ({ ...prev, titleStyle: value as any }))}
                  className={`p-3 rounded border text-center transition-colors ${
                    preferences.titleStyle === value
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="font-medium text-sm">{label}</div>
                  <div className="text-xs text-gray-500">{desc}</div>
                </button>
              ))}
            </div>
          </Card>
        </div>
      )
    },
    {
      id: "demo",
      title: "Try It Out!",
      description: "Here's a sample video to see ChapterGen in action.",
      content: (
        <div className="space-y-4">
          <Card className="p-4">
            <div className="aspect-video rounded overflow-hidden border bg-black/5 mb-3">
              <iframe
                className="w-full h-full"
                src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                title="Sample video"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              />
            </div>
            <div className="text-sm text-gray-600">
              This is a sample video that demonstrates how ChapterGen works. 
              In the real app, you'll paste your own YouTube URLs.
            </div>
          </Card>
          
          <div className="bg-gray-50 dark:bg-gray-800 rounded p-3">
            <div className="text-sm font-medium mb-2">Sample Generated Chapters:</div>
            <div className="text-xs font-mono space-y-1">
              <div>0:00 - Introduction</div>
              <div>0:30 - Main Topic Overview</div>
              <div>2:15 - Key Points Discussion</div>
              <div>4:45 - Conclusion</div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    // Save preferences to localStorage
    localStorage.setItem("userPreferences", JSON.stringify(preferences));
    localStorage.setItem("onboardingCompleted", "true");
    onComplete();
    onClose();
  };

  const handleSkip = () => {
    localStorage.setItem("onboardingCompleted", "true");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>{steps[currentStep].title}</DialogTitle>
              <DialogDescription>{steps[currentStep].description}</DialogDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={handleSkip}>
              Skip
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Step {currentStep + 1} of {steps.length}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          <div className="min-h-[300px]">
            {steps[currentStep].content}
          </div>
        </div>

        <DialogFooter>
          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            
            <Button
              onClick={handleNext}
              className="flex items-center gap-1"
            >
              {currentStep === steps.length - 1 ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Get Started
                </>
              ) : (
                <>
                  Next
                  <ChevronRight className="h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
