"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

type User = {
  id: string;
  email: string;
  customer_id: string;
  plan: string;
};

export function HeaderAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  async function checkAuth() {
    try {
      const res = await fetch("/api/auth/me", { cache: "no-store" });

      if (res.ok) {
        const data = await res.json();
        setUser(data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
    } finally {
      setLoading(false);
    }
  }

  function signOut() {
    fetch("/api/auth/signout", { method: "POST" })
      .finally(() => {
        setUser(null);
        window.location.href = "/";
      });
  }

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-16 h-8 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center gap-2">
        <Button asChild size="sm" variant="ghost"><Link href="/signin">Sign in</Link></Button>
        <Button asChild size="sm"><Link href="/signup">Sign up</Link></Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 hidden sm:inline">
        {user.email} ({user.plan})
      </span>
      <Button asChild size="sm" variant="ghost"><Link href="/account">Account</Link></Button>
      <Button size="sm" variant="ghost" onClick={signOut}>Sign out</Button>
    </div>
  );
}
