"use client";
import { useEffect, useState } from "react";
import { Progress } from "@/components/ui/progress";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Clock, Loader2, AlertCircle } from "lucide-react";

type ProcessingStep = {
  id: string;
  label: string;
  description: string;
  status: "pending" | "processing" | "completed" | "error";
};

type ProcessingProgressProps = {
  jobId: string;
  status: string;
  estimatedMinutes?: number;
  onCancel?: () => void;
  className?: string;
};

const PROCESSING_STEPS: ProcessingStep[] = [
  {
    id: "fetch",
    label: "Fetching video",
    description: "Getting video metadata and duration",
    status: "pending"
  },
  {
    id: "transcribe", 
    label: "Transcribing audio",
    description: "Converting speech to text for better accuracy",
    status: "pending"
  },
  {
    id: "segment",
    label: "Computing segments",
    description: "Analyzing content to create meaningful chapters",
    status: "pending"
  },
  {
    id: "refine",
    label: "Refining titles",
    description: "Improving chapter titles for readability",
    status: "pending"
  }
];

export function ProcessingProgress({ 
  jobId, 
  status, 
  estimatedMinutes = 0, 
  onCancel,
  className 
}: ProcessingProgressProps) {
  const [steps, setSteps] = useState<ProcessingStep[]>(PROCESSING_STEPS);
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(estimatedMinutes * 60);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
      if (estimatedTimeRemaining > 0) {
        setEstimatedTimeRemaining(prev => Math.max(0, prev - 1));
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [estimatedTimeRemaining]);

  useEffect(() => {
    // Update steps based on job status
    const newSteps = [...PROCESSING_STEPS];
    let currentProgress = 0;

    if (status === "processing" || status === "completed") {
      // Simulate step progression based on time elapsed
      const stepDuration = (estimatedMinutes * 60) / 4; // Divide total time by 4 steps
      const currentStepIndex = Math.min(3, Math.floor(timeElapsed / stepDuration));
      
      newSteps.forEach((step, index) => {
        if (index < currentStepIndex) {
          step.status = "completed";
          currentProgress += 25;
        } else if (index === currentStepIndex) {
          step.status = "processing";
          currentProgress += (timeElapsed % stepDuration) / stepDuration * 25;
        }
      });

      if (status === "completed") {
        newSteps.forEach(step => step.status = "completed");
        currentProgress = 100;
      }
    }

    if (status === "failed") {
      const failedStepIndex = Math.floor(Math.random() * 4); // Random for demo
      newSteps[failedStepIndex].status = "error";
    }

    setSteps(newSteps);
    setProgress(currentProgress);
  }, [status, timeElapsed, estimatedMinutes]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepIcon = (step: ProcessingStep) => {
    switch (step.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "processing":
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Processing Video</h3>
          <div className="text-sm text-gray-500">
            Job: <span className="font-mono">{jobId}</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Time elapsed:</span>
            <span className="font-mono">{formatTime(timeElapsed)}</span>
          </div>
          {estimatedTimeRemaining > 0 && (
            <div className="flex justify-between">
              <span className="text-gray-600">Est. remaining:</span>
              <span className="font-mono">{formatTime(estimatedTimeRemaining)}</span>
            </div>
          )}
        </div>

        <div className="space-y-3">
          {steps.map((step) => (
            <div key={step.id} className="flex items-start gap-3">
              {getStepIcon(step)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className={`font-medium ${
                    step.status === "completed" ? "text-green-600" :
                    step.status === "processing" ? "text-blue-600" :
                    step.status === "error" ? "text-red-600" :
                    "text-gray-500"
                  }`}>
                    {step.label}
                  </span>
                  {step.status === "processing" && (
                    <span className="text-xs text-blue-600 animate-pulse">
                      In progress...
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        {onCancel && status === "processing" && (
          <div className="pt-2 border-t">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onCancel}
              className="w-full"
            >
              Cancel Processing
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
}
