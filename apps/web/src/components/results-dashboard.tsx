"use client";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatChaptersForYouTube } from "@/lib/format";
import { generateSEOTitle } from "@/lib/titles";
import {
  Copy,
  Download,
  Share2,
  Play,
  Clock,
  FileText,
  CheckCircle,
  ExternalLink,
  Search,
  TrendingUp
} from "lucide-react";

type Chapter = {
  start: string;
  end: string;
  duration_sec: number;
  title: string;
  summary?: string;
};

type ResultsDashboardProps = {
  jobId: string;
  videoId: string;
  chapters: Chapter[];
  minutesBilled?: number;
  customerId?: string;
  videoTitle?: string;
  className?: string;
};

export function ResultsDashboard({
  jobId,
  videoId,
  chapters,
  minutesBilled,
  customerId,
  videoTitle,
  className
}: ResultsDashboardProps) {
  const [copiedItem, setCopiedItem] = useState<string | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<"youtube" | "json" | "txt">("youtube");

  const handleCopy = async (text: string, itemName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(itemName);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  const handleDownload = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };

  const getFormattedContent = () => {
    switch (selectedFormat) {
      case "youtube":
        return formatChaptersForYouTube(chapters);
      case "json":
        return JSON.stringify({ jobId, videoId, chapters, minutesBilled }, null, 2);
      case "txt":
        return chapters.map(c => `${c.start} - ${c.title}`).join('\n');
      default:
        return "";
    }
  };

  const totalDuration = chapters.reduce((sum, chapter) => sum + chapter.duration_sec, 0);
  const averageChapterLength = totalDuration / chapters.length;
  const seoTitle = generateSEOTitle(chapters, videoTitle);

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Header with stats */}
        <Card className="p-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div>
              <h2 className="text-lg font-semibold">Chapter Generation Complete</h2>
              <p className="text-sm text-gray-600">
                Generated {chapters.length} chapters for video {videoId}
              </p>
            </div>
            <div className="flex items-center gap-4 text-sm">
              {minutesBilled != null && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>{minutesBilled} min billed</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-green-600">Success</span>
              </div>
            </div>
          </div>
        </Card>

        {/* SEO Title Section */}
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Search className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium">SEO-Optimized Title</h3>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 mb-3">
            <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
              {seoTitle}
            </p>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-xs text-gray-500">
              Perfect for YouTube descriptions and social media sharing
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCopy(seoTitle, "seo-title")}
              className="flex items-center gap-1"
            >
              {copiedItem === "seo-title" ? (
                <CheckCircle className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
              {copiedItem === "seo-title" ? "Copied!" : "Copy Title"}
            </Button>
          </div>
        </Card>

        {/* Chapter analytics */}
        <Card className="p-4">
          <h3 className="font-medium mb-3">Chapter Analytics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Total Chapters</div>
              <div className="text-lg font-semibold">{chapters.length}</div>
            </div>
            <div>
              <div className="text-gray-600">Total Duration</div>
              <div className="text-lg font-semibold">{Math.round(totalDuration / 60)}m</div>
            </div>
            <div>
              <div className="text-gray-600">Avg Length</div>
              <div className="text-lg font-semibold">{Math.round(averageChapterLength)}s</div>
            </div>
            <div>
              <div className="text-gray-600">Density</div>
              <div className="text-lg font-semibold">
                {averageChapterLength < 120 ? "Dense" : averageChapterLength < 300 ? "Medium" : "Sparse"}
              </div>
            </div>
          </div>
        </Card>

        {/* Interactive chapter timeline */}
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <TrendingUp className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium">Chapter Timeline</h3>
            <span className="text-xs text-gray-500">({chapters.length} chapters)</span>
          </div>
          <div className="space-y-1">
            {chapters.map((chapter, index) => {
              const durationMinutes = Math.floor(chapter.duration_sec / 60);
              const durationSeconds = chapter.duration_sec % 60;
              const durationDisplay = durationMinutes > 0
                ? `${durationMinutes}m ${durationSeconds}s`
                : `${durationSeconds}s`;

              // Color code based on chapter length
              const getDurationColor = (duration: number) => {
                if (duration < 60) return "text-orange-500"; // Short
                if (duration < 180) return "text-green-500"; // Good
                if (duration < 300) return "text-blue-500"; // Long
                return "text-purple-500"; // Very long
              };

              return (
                <div key={index} className="group flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                  <div className="flex-shrink-0 flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-medium text-blue-600">
                      {index + 1}
                    </div>
                    <div className="text-xs font-mono text-gray-500 min-w-[50px]">
                      {chapter.start}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate text-sm">{chapter.title}</div>
                    {chapter.summary && (
                      <div className="text-xs text-gray-500 truncate mt-1">{chapter.summary}</div>
                    )}
                  </div>
                  <div className={`flex-shrink-0 text-xs font-medium ${getDurationColor(chapter.duration_sec)}`}>
                    {durationDisplay}
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => window.open(`https://youtube.com/watch?v=${videoId}&t=${chapter.start.replace(':', 'm')}s`, '_blank')}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Play from this timestamp</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
            ))}
          </div>
        </Card>

        {/* Export interface */}
        <Card className="p-4">
          <h3 className="font-medium mb-3">Export Chapters</h3>
          
          {/* Format selector */}
          <div className="flex gap-2 mb-4">
            {[
              { id: "youtube", label: "YouTube Description", icon: FileText },
              { id: "json", label: "JSON", icon: FileText },
              { id: "txt", label: "Plain Text", icon: FileText }
            ].map(({ id, label, icon: Icon }) => (
              <Button
                key={id}
                variant={selectedFormat === id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFormat(id as any)}
                className="flex items-center gap-1"
              >
                <Icon className="h-3 w-3" />
                {label}
              </Button>
            ))}
          </div>

          {/* Preview */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">Preview:</div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded p-3 text-xs font-mono max-h-32 overflow-y-auto">
              <pre className="whitespace-pre-wrap">{getFormattedContent().slice(0, 200)}...</pre>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => handleCopy(getFormattedContent(), selectedFormat)}
              className="flex items-center gap-1"
            >
              {copiedItem === selectedFormat ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copiedItem === selectedFormat ? "Copied!" : "Copy"}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleDownload(
                getFormattedContent(),
                `chapters_${videoId}.${selectedFormat === "json" ? "json" : "txt"}`,
                selectedFormat === "json" ? "application/json" : "text/plain"
              )}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                const shareUrl = `${window.location.origin}/videos/${videoId}`;
                handleCopy(shareUrl, "share");
              }}
              className="flex items-center gap-1"
            >
              {copiedItem === "share" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Share2 className="h-4 w-4" />
              )}
              {copiedItem === "share" ? "Link Copied!" : "Share"}
            </Button>

            <Button
              variant="outline"
              onClick={() => window.open(`https://youtube.com/watch?v=${videoId}`, '_blank')}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              View Video
            </Button>
          </div>
        </Card>
      </div>
    </TooltipProvider>
  );
}
