"use client";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatChaptersForYouTube } from "@/lib/format";
import { 
  Copy, 
  Download, 
  Share2, 
  Play, 
  Clock, 
  FileText, 
  CheckCircle,
  ExternalLink 
} from "lucide-react";

type Chapter = {
  start: string;
  end: string;
  duration_sec: number;
  title: string;
  summary?: string;
};

type ResultsDashboardProps = {
  jobId: string;
  videoId: string;
  chapters: Chapter[];
  minutesBilled?: number;
  customerId?: string;
  className?: string;
};

export function ResultsDashboard({
  jobId,
  videoId,
  chapters,
  minutesBilled,
  customerId,
  className
}: ResultsDashboardProps) {
  const [copiedItem, setCopiedItem] = useState<string | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<"youtube" | "json" | "txt">("youtube");

  const handleCopy = async (text: string, itemName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(itemName);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  const handleDownload = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };

  const getFormattedContent = () => {
    switch (selectedFormat) {
      case "youtube":
        return formatChaptersForYouTube(chapters);
      case "json":
        return JSON.stringify({ jobId, videoId, chapters, minutesBilled }, null, 2);
      case "txt":
        return chapters.map(c => `${c.start} - ${c.title}`).join('\n');
      default:
        return "";
    }
  };

  const totalDuration = chapters.reduce((sum, chapter) => sum + chapter.duration_sec, 0);
  const averageChapterLength = totalDuration / chapters.length;

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Header with stats */}
        <Card className="p-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div>
              <h2 className="text-lg font-semibold">Chapter Generation Complete</h2>
              <p className="text-sm text-gray-600">
                Generated {chapters.length} chapters for video {videoId}
              </p>
            </div>
            <div className="flex items-center gap-4 text-sm">
              {minutesBilled != null && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>{minutesBilled} min billed</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-green-600">Success</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Chapter analytics */}
        <Card className="p-4">
          <h3 className="font-medium mb-3">Chapter Analytics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Total Chapters</div>
              <div className="text-lg font-semibold">{chapters.length}</div>
            </div>
            <div>
              <div className="text-gray-600">Total Duration</div>
              <div className="text-lg font-semibold">{Math.round(totalDuration / 60)}m</div>
            </div>
            <div>
              <div className="text-gray-600">Avg Length</div>
              <div className="text-lg font-semibold">{Math.round(averageChapterLength)}s</div>
            </div>
            <div>
              <div className="text-gray-600">Density</div>
              <div className="text-lg font-semibold">
                {averageChapterLength < 120 ? "Dense" : averageChapterLength < 300 ? "Medium" : "Sparse"}
              </div>
            </div>
          </div>
        </Card>

        {/* Interactive chapter timeline */}
        <Card className="p-4">
          <h3 className="font-medium mb-3">Chapter Timeline</h3>
          <div className="space-y-2">
            {chapters.map((chapter, index) => (
              <div key={index} className="group flex items-center gap-3 p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800/50">
                <div className="flex-shrink-0 w-16 text-xs font-mono text-gray-500">
                  {chapter.start}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{chapter.title}</div>
                  {chapter.summary && (
                    <div className="text-xs text-gray-500 truncate">{chapter.summary}</div>
                  )}
                </div>
                <div className="flex-shrink-0 text-xs text-gray-400">
                  {Math.round(chapter.duration_sec)}s
                </div>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => window.open(`https://youtube.com/watch?v=${videoId}&t=${chapter.start.replace(':', 'm')}s`, '_blank')}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Play from this timestamp</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            ))}
          </div>
        </Card>

        {/* Export interface */}
        <Card className="p-4">
          <h3 className="font-medium mb-3">Export Chapters</h3>
          
          {/* Format selector */}
          <div className="flex gap-2 mb-4">
            {[
              { id: "youtube", label: "YouTube Description", icon: FileText },
              { id: "json", label: "JSON", icon: FileText },
              { id: "txt", label: "Plain Text", icon: FileText }
            ].map(({ id, label, icon: Icon }) => (
              <Button
                key={id}
                variant={selectedFormat === id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFormat(id as any)}
                className="flex items-center gap-1"
              >
                <Icon className="h-3 w-3" />
                {label}
              </Button>
            ))}
          </div>

          {/* Preview */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">Preview:</div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded p-3 text-xs font-mono max-h-32 overflow-y-auto">
              <pre className="whitespace-pre-wrap">{getFormattedContent().slice(0, 200)}...</pre>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => handleCopy(getFormattedContent(), selectedFormat)}
              className="flex items-center gap-1"
            >
              {copiedItem === selectedFormat ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copiedItem === selectedFormat ? "Copied!" : "Copy"}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleDownload(
                getFormattedContent(),
                `chapters_${videoId}.${selectedFormat === "json" ? "json" : "txt"}`,
                selectedFormat === "json" ? "application/json" : "text/plain"
              )}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                const shareUrl = `${window.location.origin}/videos/${videoId}`;
                handleCopy(shareUrl, "share");
              }}
              className="flex items-center gap-1"
            >
              {copiedItem === "share" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Share2 className="h-4 w-4" />
              )}
              {copiedItem === "share" ? "Link Copied!" : "Share"}
            </Button>

            <Button
              variant="outline"
              onClick={() => window.open(`https://youtube.com/watch?v=${videoId}`, '_blank')}
              className="flex items-center gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              View Video
            </Button>
          </div>
        </Card>
      </div>
    </TooltipProvider>
  );
}
