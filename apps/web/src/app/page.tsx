import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Play, Clock, Download, Star, Users, CheckCircle } from "lucide-react";

export default function Landing() {
  return (
    <div className="min-h-screen">
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(14,165,233,0.10),transparent_60%),radial-gradient(ellipse_at_bottom,rgba(99,102,241,0.10),transparent_60%)]" />
        <div className="relative max-w-6xl mx-auto px-4 py-16 sm:py-20 lg:py-28">
          <div className="text-center lg:text-left">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-semibold tracking-tight">
              Turn any YouTube video into clean, copy‑ready chapters
            </h1>
            <p className="mt-4 text-base lg:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto lg:mx-0">
              Paste a URL. Get timestamped sections and better titles in seconds. Export as YouTube
              description, JSON, or TXT.
            </p>

            {/* Social proof */}
            <div className="mt-6 flex flex-wrap items-center justify-center lg:justify-start gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>10,000+ videos processed</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>4.8/5 rating</span>
              </div>
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>99.9% uptime</span>
              </div>
            </div>

            <div className="mt-8 flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
              <Button asChild size="lg" className="w-full sm:w-auto">
                <Link href="/app" className="flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Get started free
                </Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="w-full sm:w-auto">
                <Link href="/pricing">See pricing</Link>
              </Button>
            </div>
            <p className="mt-3 text-xs text-gray-500 text-center lg:text-left">
              No signup needed for demo. Starter plan: 120 min/month.
            </p>
          </div>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-medium">Fast & predictable</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Deterministic segmentation with transcript‑aware refinement when available.
            </p>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <Download className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-medium">Export anywhere</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Copy as YouTube description or download as TXT/JSON for your workflow.
            </p>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow sm:col-span-2 lg:col-span-1">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <Star className="h-5 w-5 text-purple-600" />
              </div>
              <h3 className="font-medium">Fair billing</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Only minutes processed count. Plans for 120/400/1600 minutes.
            </p>
          </Card>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-4 pb-16">
        <Card className="p-6 lg:p-8">
          <h3 className="font-medium text-lg mb-4">How it works</h3>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {[
              {
                step: "1",
                title: "Paste URL",
                description: "Add your YouTube link to the generator",
                color: "bg-blue-100 text-blue-600 dark:bg-blue-900"
              },
              {
                step: "2",
                title: "Process",
                description: "We fetch duration and transcribe audio",
                color: "bg-green-100 text-green-600 dark:bg-green-900"
              },
              {
                step: "3",
                title: "Generate",
                description: "AI creates meaningful chapter segments",
                color: "bg-purple-100 text-purple-600 dark:bg-purple-900"
              },
              {
                step: "4",
                title: "Export",
                description: "Copy or download in your preferred format",
                color: "bg-orange-100 text-orange-600 dark:bg-orange-900"
              }
            ].map(({ step, title, description, color }) => (
              <div key={step} className="text-center">
                <div className={`w-12 h-12 ${color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <span className="font-semibold">{step}</span>
                </div>
                <h4 className="font-medium mb-1">{title}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">{description}</p>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center">
            <Button asChild>
              <Link href="/app" className="flex items-center gap-2">
                <Play className="h-4 w-4" />
                Try it now
              </Link>
            </Button>
          </div>
        </Card>
      </section>
    </div>
  );
}
