import { parseAuth } from "@/lib/auth";
import { getUsageSummary } from "@/lib/billing";
import { listUsageRecords } from "@/lib/stripe";
import { getBonusRemaining } from "@/lib/billing";

export async function GET(req: Request) {
  try {
    const auth = await parseAuth(req.headers);

    if (!auth.is_authenticated) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const summary = getUsageSummary(auth.customer_id, auth.plan);
    const records = listUsageRecords();
    return Response.json({ ...summary, records, bonus_remaining: getBonusRemaining(auth.customer_id) }, { status: 200 });
  } catch (error) {
    console.error('Usage API error:', error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}


