import { NextRequest } from "next/server";

import {
  saveJob,
  getJob,
  updateJob<PERSON>tatus,
  completeJob
} from "@/lib/storage";
import { JobRecord } from "@/lib/types";
import { extractYouTubeVideoId } from "@/lib/url";
import { getIdempotentJobId, makeIdempotencyKey, rememberIdempotency } from "@/lib/idempotency";
import { parseAuth } from "@/lib/auth";
import { MAX_SINGLE_VIDEO_MINUTES, minutesFromSeconds, mockLookupVideoDurationSeconds } from "@/lib/limits";
import { canConsume, recordUsage } from "@/lib/billing";
import { postUsageRecord } from "@/lib/stripe";
import { publish, drainQueue } from "@/lib/bus";
import { segmentByLength, segmentByTranscript, computeChapterCount, generateChaptersWithOpenAI, enhanceChaptersWithOpenAI } from "@/lib/chapters";
import { refineChapterTitles } from "@/lib/titles";
import { fetchYouTubeDurationSeconds } from "@/lib/youtube";
import { broadcastJobCompleted } from "@/lib/realtime";
// import { transcribeWithGroqFromUrl } from "@/lib/transcribe";
import { fetchYouTubeAudioAsFile } from "@/lib/audio";
import { getCachedTranscript, setCachedTranscript } from "@/lib/transcriptStore";
import { saveChapters as persistChapters } from "@/lib/chaptersStore";
import { saveVideo as persistVideo } from "@/lib/videosStore";
import { rateLimit, rateLimitCheck, getClientIP, RATE_LIMITS, createRateLimitResponse } from "@/lib/rateLimit";
import { logger } from "@/lib/logger";

export const runtime = "nodejs";

type GenerateRequestBody = {
  url: string;
  options?: {
    chapter_density?: "auto" | "sparse" | "dense";
    language_hint?: string;
    title_style?: "concise" | "descriptive";
    include_summaries?: boolean;
  };
};

function withHeaders(body: any, status = 200, requestId?: string) {
  const headers: Record<string, string> = { "Cache-Control": "no-store" };
  if (requestId) headers["X-Request-Id"] = requestId;
  return Response.json(body, { status, headers });
}

export async function POST(req: NextRequest) {
  try {
    const requestId = crypto.randomUUID();
    // Rate limiting - IP-based
    const clientIP = getClientIP(req.headers);
    const ipRateLimit = await rateLimitCheck(`generate:ip:${clientIP}`, RATE_LIMITS.GENERATE_API);

    if (!ipRateLimit.allowed) {
      logger.warn("Rate limit exceeded (IP)", { requestId, clientIP });
      return createRateLimitResponse(ipRateLimit.resetTime);
    }

    const body = (await req.json()) as GenerateRequestBody;
    if (!body?.url || typeof body.url !== "string") {
      return withHeaders({ error: "Invalid request: 'url' is required" }, 400, requestId);
    }

    // Validate and canonicalize URL (extract video_id)
    const videoId = extractYouTubeVideoId(body.url);
    if (!videoId) {
      return withHeaders({ error: "Could not extract video_id from URL" }, 400, requestId);
    }

    // Parse auth and check authentication
    const auth = await parseAuth(req.headers);
    if (!auth.is_authenticated) {
      return withHeaders({ error: "Authentication required" }, 401, requestId);
    }

    // Rate limiting - User-based
    const userRateLimit = await rateLimitCheck(`generate:user:${auth.user_id}`, RATE_LIMITS.USER_GENERATE);

    if (!userRateLimit.allowed) {
      logger.warn("Rate limit exceeded (user)", { requestId, userId: auth.user_id });
      return createRateLimitResponse(userRateLimit.resetTime);
    }

    // Idempotency: Use header or derive from (video_id, options)
    const headerIdem = req.headers.get("idempotency-key") || undefined;
    const optionsFingerprint = JSON.stringify(body.options || {});
    const generatedKey = makeIdempotencyKey([videoId, optionsFingerprint]);
    const idemKey = headerIdem || generatedKey;
    const existingJobId = getIdempotentJobId(idemKey);
    if (existingJobId) {
      const existing = getJob(existingJobId);
      if (existing) {
        logger.info("Idempotent replay: returning existing job", { requestId, jobId: existingJobId });
        return withHeaders(existing, 202, requestId);
      }
    }

    // Length gate: try real duration via YouTube API, fallback to mock
    const realSec = await fetchYouTubeDurationSeconds(videoId);
    const durationSec = realSec ?? mockLookupVideoDurationSeconds(videoId);
    const durationMin = minutesFromSeconds(durationSec);
    if (durationMin > MAX_SINGLE_VIDEO_MINUTES) {
      return withHeaders({ error: "Video too long" }, 413, requestId);
    }

    // If plan allowance insufficient, block with 402 for now (demo behavior)
    if (!canConsume(auth.customer_id, auth.plan, durationMin)) {
      return withHeaders(
        {
          error: "Quota exceeded",
          code: "QUOTA_EXCEEDED",
          minutes_needed: durationMin,
          remaining_minutes: 0,
          plan: auth.plan,
          message:
            "Your monthly allowance is insufficient for this video. Upgrade plan or wait for reset.",
        },
        402,
        requestId
      );
    }

    // Create job record and persist to in-memory store
    const jobId = `job_${Math.random().toString(36).slice(2, 10)}`;
    const estimatedMinutes = durationMin;
    const nowIso = new Date().toISOString();

    const record: JobRecord = {
      job_id: jobId,
      video_id: videoId,
      status: "queued",
      estimated_minutes: estimatedMinutes,
      billing_preview_minutes: estimatedMinutes,
      created_at: nowIso,
      customer_id: auth.customer_id,
    };

    saveJob(record);
    rememberIdempotency(idemKey, jobId);
    // Save video metadata
    try { await persistVideo({ video_id: videoId, duration_sec: durationSec, created_at: nowIso, customer_id: auth.customer_id }); } catch {}
    publish({
      type: "VideoSubmitted",
      payload: {
        jobId,
        videoId,
        estimatedMinutes,
        optionsJson: optionsFingerprint,
        auth,
      },
    });
    // Auto-drain in dev to simulate orchestration without separate call
    queueMicrotask(() => {
      void drainQueue();
    });
    // Simulate background processing: mark processing, then complete
    queueMicrotask(() => {
      updateJobStatus(jobId, "processing");
      setTimeout(async () => {
        let demoChapters;
        let usedOpenAI = false; // Track if OpenAI was used for title generation

        // Optional Groq integration if GROQ_API_KEY and AUDIO_URL provided
        const ytUrl = body.url;
        if (ytUrl && process.env.GROQ_API_KEY) {
          const cachedMaybe = getCachedTranscript(videoId);
          const cached = (cachedMaybe && typeof (cachedMaybe as any).then === "function") ? await (cachedMaybe as any) : cachedMaybe;
          if (cached?.segments?.length) {
            const target = computeChapterCount(durationMin, body.options?.chapter_density || "auto");
            // Try OpenAI JSON chaptering first with cached transcript
            console.log('🤖 Attempting OpenAI chapter generation with cached transcript...');
            const aiChapters = await generateChaptersWithOpenAI(cached.segments, {
              titleStyle: body.options?.title_style || 'concise',
              targetCount: target,
            });
            if (aiChapters) {
              console.log('✅ OpenAI generated chapters:', aiChapters.map(c => c.title));
              demoChapters = aiChapters;
              usedOpenAI = true;
            } else {
              console.log('❌ OpenAI failed, using transcript segmentation');
              demoChapters = segmentByTranscript(cached.segments, target);
            }
          }
        }
        if (!demoChapters && ytUrl && process.env.GROQ_API_KEY) {
          // Fetch best audio stream and pass to Groq
          try {
            const infoUrl = `https://www.youtube.com/watch?v=${videoId}`;
            const audioFile = await fetchYouTubeAudioAsFile(infoUrl);
            const mod = await import("groq-sdk");
            const Groq = (mod as any).default || (mod as any);
            const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });
            const resp = await (groq as any).audio.transcriptions.create({
              file: audioFile,
              model: process.env.GROQ_WHISPER_MODEL || "whisper-large-v3",
              response_format: "verbose_json",
            });
            const segments = resp?.segments?.map((s: any) => ({ start: Number(s.start || 0), end: Number(s.end || 0), text: String(s.text || "") }));
            if (segments?.length) {
              setCachedTranscript(videoId, segments, resp?.language);
              const target = computeChapterCount(durationMin, body.options?.chapter_density || "auto");
              // Try OpenAI JSON chaptering first
              console.log('🤖 Attempting OpenAI chapter generation with fresh transcript...');
              const aiChapters = await generateChaptersWithOpenAI(segments, {
                titleStyle: body.options?.title_style || 'concise',
                targetCount: target,
              });
              if (aiChapters) {
                console.log('✅ OpenAI generated chapters:', aiChapters.map(c => c.title));
                demoChapters = aiChapters;
                usedOpenAI = true;
              } else {
                console.log('❌ OpenAI failed, using transcript segmentation');
                demoChapters = segmentByTranscript(segments, target);
              }
            }
          } catch {
            // fall back silently
          }
        }
        if (!demoChapters) {
          console.log('🔧 No transcript available, using length-based segmentation');
          demoChapters = segmentByLength(durationSec, body.options?.chapter_density || "auto");

          // Try to enhance basic chapters with OpenAI if available
          if (process.env.OPENROUTER_API_KEY) {
            console.log('🤖 Attempting to enhance basic chapters with OpenAI...');
            try {
              const enhancedChapters = await enhanceChaptersWithOpenAI(demoChapters, {
                titleStyle: body.options?.title_style || 'concise',
                videoId: videoId,
                duration: durationSec
              });
              if (enhancedChapters) {
                console.log('✅ OpenAI enhanced basic chapters');
                demoChapters = enhancedChapters;
                usedOpenAI = true;
              }
            } catch (error) {
              console.log('❌ OpenAI enhancement failed:', error);
            }
          }
        }

        // Only refine titles if they weren't generated by OpenAI (OpenAI titles are already high-quality)
        console.log(`🔧 Title refinement: usedOpenAI=${usedOpenAI}, will ${usedOpenAI ? 'SKIP' : 'APPLY'} refineChapterTitles`);
        if (!usedOpenAI) {
          console.log('🔧 Applying refineChapterTitles to basic chapters');
          demoChapters = refineChapterTitles(demoChapters, body.options?.title_style || "concise");
        } else {
          console.log('✅ Preserving OpenAI-generated titles');
        }
        console.log('📋 Final chapter titles:', demoChapters.map(c => c.title));
        if (body.options?.include_summaries) {
          demoChapters = demoChapters.map((c, i) => ({
            ...c,
            summary: `Overview of ${c.title.toLowerCase()} (segment ${i + 1}).`,
          }));
        }
        if (canConsume(auth.customer_id, auth.plan, estimatedMinutes)) {
          recordUsage(auth.customer_id, estimatedMinutes);
          void postUsageRecord({
            customer_id: auth.customer_id,
            minutes: estimatedMinutes,
            timestamp: Date.now(),
            job_id: jobId,
          });
        }
        completeJob(jobId, demoChapters, estimatedMinutes);
        // Persist chapters separately for listing/querying
        try { await persistChapters(videoId, demoChapters); } catch {}
        // Best-effort real-time push to connected clients
        try {
          const latest = getJob(jobId);
          if (latest) broadcastJobCompleted(latest as any);
        } catch {}
      }, 1500);
    });

    return withHeaders(record, 202, requestId);
  } catch (err) {
    logger.error('Generate API error', { error: (err as any)?.message });
    return Response.json({ error: "Bad Request" }, { status: 400, headers: { "Cache-Control": "no-store" } });
  }
}


