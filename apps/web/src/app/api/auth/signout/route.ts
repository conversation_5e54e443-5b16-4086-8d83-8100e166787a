export const runtime = "nodejs";

export async function POST() {
  const headers = new Headers();
  // Clear the auth cookie with multiple variations to ensure it's removed
  headers.append(
    "Set-Cookie",
    `auth_token=; HttpOnly; Path=/; Max-Age=0; SameSite=Lax; Secure; Expires=Thu, 01 Jan 1970 00:00:00 GMT`
  );
  // Also clear without Secure flag for development
  headers.append(
    "Set-Cookie",
    `auth_token=; HttpOnly; Path=/; Max-Age=0; SameSite=Lax; Expires=Thu, 01 Jan 1970 00:00:00 GMT`
  );
  return Response.json({ success: true }, { status: 200, headers });
}


