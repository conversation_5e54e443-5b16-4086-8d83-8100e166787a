export async function GET() {
  return Response.json({
    hasGroq: <PERSON><PERSON><PERSON>(process.env.GROQ_API_KEY),
    hasOpenRouter: <PERSON><PERSON><PERSON>(process.env.OPENROUTER_API_KEY), 
    hasOpen<PERSON><PERSON>: <PERSON><PERSON><PERSON>(process.env.OPENAI_API_KEY),
    groqPrefix: process.env.GROQ_API_KEY?.slice(0, 10) || 'none',
    openRouterPrefix: process.env.OPENROUTER_API_KEY?.slice(0, 10) || 'none',
    openAIPrefix: process.env.OPENAI_API_KEY?.slice(0, 10) || 'none',
    nodeEnv: process.env.NODE_ENV
  });
}
