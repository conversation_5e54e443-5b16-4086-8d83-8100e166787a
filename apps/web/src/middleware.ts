import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyJWT } from '@/lib/auth';

export async function middleware(request: NextRequest) {
  // Only check auth for protected routes
  const protectedPaths = ['/app', '/account', '/api/generate', '/api/usage'];
  const isProtectedPath = protectedPaths.some(path => 
    request.nextUrl.pathname.startsWith(path)
  );

  if (!isProtectedPath) {
    return NextResponse.next();
  }

  // Check for auth token in cookies
  const authToken = request.cookies.get('auth_token')?.value;
  
  if (authToken) {
    try {
      const jwtPayload = await verifyJWT(authToken);
      
      // If token is expired, clear the cookie and redirect to signin
      if (jwtPayload?.expired) {
        console.log('Expired token detected, clearing cookie');
        
        const response = NextResponse.redirect(new URL('/signin?message=Session expired. Please sign in again.', request.url));
        
        // Clear the expired cookie
        response.cookies.set('auth_token', '', {
          httpOnly: true,
          path: '/',
          maxAge: 0,
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production'
        });
        
        return response;
      }
      
      // If token is invalid (not just expired), also clear and redirect
      if (!jwtPayload) {
        console.log('Invalid token detected, clearing cookie');
        
        const response = NextResponse.redirect(new URL('/signin?message=Invalid session. Please sign in again.', request.url));
        
        response.cookies.set('auth_token', '', {
          httpOnly: true,
          path: '/',
          maxAge: 0,
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production'
        });
        
        return response;
      }
    } catch (error) {
      console.error('Middleware auth check failed:', error);
      
      // Clear potentially corrupted cookie
      const response = NextResponse.redirect(new URL('/signin?message=Authentication error. Please sign in again.', request.url));
      
      response.cookies.set('auth_token', '', {
        httpOnly: true,
        path: '/',
        maxAge: 0,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
      });
      
      return response;
    }
  } else {
    // No auth token for protected route
    if (request.nextUrl.pathname.startsWith('/api/')) {
      // For API routes, return 401
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    } else {
      // For pages, redirect to signin
      return NextResponse.redirect(new URL('/signin', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/app/:path*',
    '/account/:path*',
    '/api/generate/:path*',
    '/api/usage/:path*'
  ]
};
