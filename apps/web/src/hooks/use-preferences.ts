"use client";
import { useState, useEffect } from "react";

export type UserPreferences = {
  chapterDensity: "auto" | "sparse" | "dense";
  titleStyle: "concise" | "descriptive";
  emailNotifications: boolean;
  onboardingCompleted: boolean;
  theme: "light" | "dark" | "system";
  exportFormat: "youtube" | "json" | "txt";
};

const defaultPreferences: UserPreferences = {
  chapterDensity: "auto",
  titleStyle: "concise", 
  emailNotifications: false,
  onboardingCompleted: false,
  theme: "system",
  exportFormat: "youtube"
};

export function usePreferences() {
  const [preferences, setPreferences] = useState<UserPreferences>(defaultPreferences);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem("userPreferences");
      const onboardingCompleted = localStorage.getItem("onboardingCompleted") === "true";
      
      if (saved) {
        const parsed = JSON.parse(saved);
        setPreferences({
          ...defaultPreferences,
          ...parsed,
          onboardingCompleted
        });
      } else {
        setPreferences(prev => ({
          ...prev,
          onboardingCompleted
        }));
      }
    } catch (error) {
      console.error("Failed to load preferences:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save preferences to localStorage whenever they change
  const updatePreferences = (updates: Partial<UserPreferences>) => {
    const newPreferences = { ...preferences, ...updates };
    setPreferences(newPreferences);
    
    try {
      // Save to localStorage (excluding onboardingCompleted which is stored separately)
      const { onboardingCompleted, ...toSave } = newPreferences;
      localStorage.setItem("userPreferences", JSON.stringify(toSave));
      
      if (updates.onboardingCompleted !== undefined) {
        localStorage.setItem("onboardingCompleted", updates.onboardingCompleted.toString());
      }
    } catch (error) {
      console.error("Failed to save preferences:", error);
    }
  };

  // Reset preferences to defaults
  const resetPreferences = () => {
    setPreferences(defaultPreferences);
    try {
      localStorage.removeItem("userPreferences");
      localStorage.removeItem("onboardingCompleted");
    } catch (error) {
      console.error("Failed to reset preferences:", error);
    }
  };

  // Get smart defaults based on video analysis (placeholder for future enhancement)
  const getSmartDefaults = (videoData?: { duration?: number; category?: string }) => {
    if (!videoData) return {};
    
    const smartDefaults: Partial<UserPreferences> = {};
    
    // Suggest density based on video duration
    if (videoData.duration) {
      if (videoData.duration < 300) { // < 5 minutes
        smartDefaults.chapterDensity = "sparse";
      } else if (videoData.duration > 1800) { // > 30 minutes
        smartDefaults.chapterDensity = "dense";
      } else {
        smartDefaults.chapterDensity = "auto";
      }
    }
    
    // Suggest title style based on category
    if (videoData.category) {
      const technicalCategories = ["Education", "Science & Technology", "Howto & Style"];
      if (technicalCategories.includes(videoData.category)) {
        smartDefaults.titleStyle = "descriptive";
      } else {
        smartDefaults.titleStyle = "concise";
      }
    }
    
    return smartDefaults;
  };

  return {
    preferences,
    updatePreferences,
    resetPreferences,
    getSmartDefaults,
    isLoaded
  };
}
