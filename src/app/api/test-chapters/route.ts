import { NextRequest } from "next/server";
import { generateChaptersWithOpenAI } from "@/lib/chapters";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { segments, options } = body;

    if (!segments || !Array.isArray(segments)) {
      return Response.json(
        { error: "Segments array is required" },
        { status: 400 }
      );
    }

    console.log("🧪 Testing generateChaptersWithOpenAI with", segments.length, "segments");

    // Test the generateChaptersWithOpenAI function
    const chapters = await generateChaptersWithOpenAI(segments, options);

    if (chapters) {
      console.log("✅ generateChaptersWithOpenAI succeeded, returned", chapters.length, "chapters");
      return Response.json({
        success: true,
        chapters,
        message: "OpenAI chapter generation successful"
      });
    } else {
      console.log("❌ generateChaptersWithOpenAI returned null");
      return Response.json({
        success: false,
        chapters: null,
        message: "OpenAI chapter generation failed"
      });
    }

  } catch (error) {
    console.error("❌ Test endpoint error:", error);
    return Response.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Test endpoint failed"
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return Response.json({
    endpoint: "OpenAI Chapter Generation Test",
    description: "Tests the generateChaptersWithOpenAI function directly",
    usage: "POST with { segments: [...], options: {...} }"
  });
}
